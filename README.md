# 🚀 Nimarmi

**Nimarmi** is a professional web design and branding company specializing in beautiful websites and modern logos for small businesses. Built with **[Astro 5.0](https://astro.build/) + [Tailwind CSS](https://tailwindcss.com/)** for optimal performance and user experience.

## What We Offer

- 🎨 **Professional Web Design** - Mobile-first, fast-loading websites that convert visitors into customers
- 🏷️ **Modern Logo Design** - Beautiful, memorable logos and complete brand packages
- 🚀 **Lightning Fast Performance** - Websites that load in under 1 second
- 📱 **Mobile Optimized** - Perfect experience on all devices and screen sizes
- 🔍 **SEO Ready** - Built to be found on Google and other search engines
- 💬 **Ongoing Support** - Updates, maintenance, and support included
- 🏠 **Family-Owned Business** - Personal service from real people in Chicagoland

## 🚀 About Nimarmi

We're a family-owned web design and branding company based in Chicagoland. We specialize in helping small businesses create a professional online presence that actually drives results.

### Why Choose Nimarmi?

- **No Tech Headaches** - We handle everything so you can focus on your business
- **Transparent Pricing** - One monthly fee covers everything, no surprise charges
- **Real Support** - Talk to real people, get real help when you need it
- **Local Business** - We understand small business challenges because we are one

## 🛠️ Technical Details

This website is built with modern web technologies for optimal performance:

- **[Astro 5.0](https://astro.build/)** - Fast, modern web framework
- **[Tailwind CSS](https://tailwindcss.com/)** - Utility-first CSS framework
- **Mobile-First Design** - Optimized for all devices
- **SEO Optimized** - Built to rank well in search engines
- **Lightning Fast** - Loads in under 1 second

## 📞 Get In Touch

Ready to take your business online? Let's talk about how we can help you create a professional website and brand that drives results.

- **Website**: [nimarmi.com](https://nimarmi.com)
- **Email**: <EMAIL>
- **Phone**: Contact us through our website
- **Location**: Chicagoland area

## 🚀 Development

This website is built with modern web technologies for optimal performance and maintainability.

### Getting Started

1. **Clone the repository**

    ```bash
    git clone [repository-url]
    cd nimarmi
    ```

2. **Install dependencies**

    ```bash
    npm install
    ```

3. **Start development server**

    ```bash
    npm run dev
    ```

4. **Open your browser**
   Navigate to `http://localhost:4321`

### Available Commands

| Command           | Action                                       |
| :---------------- | :------------------------------------------- |
| `npm install`     | Installs dependencies                        |
| `npm run dev`     | Starts local dev server at `localhost:4321`  |
| `npm run build`   | Build your production site to `./dist/`      |
| `npm run preview` | Preview your build locally, before deploying |
| `npm run check`   | Check your project for errors                |
| `npm run fix`     | Run Eslint and format codes with Prettier    |

### Project Structure

Built with [Astro](https://astro.build/) and [Tailwind CSS](https://tailwindcss.com/) for optimal performance.

---

**© 2024 Nimarmi - Professional Web Design & Branding**
