---
import WidgetWrapper from "~/components/ui/WidgetWrapper.astro";
import Headline from "~/components/ui/Headline.astro";
import ItemGrid2 from "~/components/ui/ItemGrid2.astro";
import type { Features as Props } from "~/types";

const {
    title = await Astro.slots.render("title"),
    subtitle = await Astro.slots.render("subtitle"),
    link = await Astro.slots.render("link"),
    tagline = await Astro.slots.render("tagline"),
    items = [],
    columns = 3,
    defaultIcon,

    id,
    isDark = false,
    classes = {},
    bg = await Astro.slots.render("bg"),
} = Astro.props;
---

<WidgetWrapper
    id={id}
    isDark={isDark}
    containerClass={`max-w-7xl mx-auto ${classes?.container ?? ""}`}
    bg={bg}>
    <Headline
        title={title}
        link={link}
        subtitle={subtitle}
        tagline={tagline}
        classes={classes?.headline as Record<string, string>}
    />
    <ItemGrid2
        items={items}
        columns={columns}
        defaultIcon={defaultIcon}
        classes={{
            container: "gap-4 md:gap-6",
            panel: "rounded-lg shadow-[0_4px_30px_rgba(0,0,0,0.1)] dark:shadow-[0_4px_30px_rgba(0,0,0,0.1)] backdrop-blur border border-[#ffffff29] bg-white dark:bg-slate-900 p-6",
            // panel:
            //   "text-center bg-page items-center md:text-left rtl:md:text-right md:items-start p-6 p-6 rounded-md shadow-xl dark:shadow-none dark:border dark:border-slate-800",
            icon: "w-12 h-12 mb-6 text-primary",
            ...((classes?.items as Record<string, never>) ?? {}),
        }}
    />
</WidgetWrapper>
