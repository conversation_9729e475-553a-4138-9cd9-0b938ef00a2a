---
import Image from "~/components/common/Image.astro";
import Button from "~/components/ui/Button.astro";

import type { Hero as Props } from "~/types";

const {
    title = await Astro.slots.render("title"),
    subtitle = await Astro.slots.render("subtitle"),
    tagline,

    content = await Astro.slots.render("content"),
    actions = await Astro.slots.render("actions"),
    image = await Astro.slots.render("image"),

    id,
    bg = await Astro.slots.render("bg"),
} = Astro.props;
---

<section class="relative md:-mt-[96px] not-prose" {...id ? { id } : {}}>
    <div class="light-mode-only">
        <div class="aura-container">
            <div class="aura"></div>
            <!-- <div class="aura-mask"></div> -->
        </div>
    </div>
    <div class="dark-mode-only text-muted">
        <div class="stars">
            <div id="sky">
                <div class="shooting">
                    <div class="star"></div>
                </div>
                <div class="shooting">
                    <div class="star"></div>
                </div>
                <div class="shooting">
                    <div class="star"></div>
                </div>
                <div class="shooting">
                    <div class="star"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="absolute inset-0 pointer-events-none" aria-hidden="true">
        <slot name="bg">
            {bg ? <Fragment set:html={bg} /> : null}
        </slot>
    </div>
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6">
        <div class="pt-0 md:pt-[76px] pointer-events-none"></div>
        <div
            class="py-12 md:py-20 lg:py-0 lg:flex lg:items-center lg:h-screen lg:gap-8">
            <div
                class="basis-1/2 text-center lg:text-left pb-10 md:pb-16 mx-auto">
                {
                    tagline && (
                        <p
                            class="text-base mb-3 text-secondary dark:text-blue-200 font-bold tracking-wide uppercase intersect-once motion-safe:md:intersect:animate-fade motion-safe:md:opacity-0 intersect-quarter"
                            set:html={tagline}
                        />
                    )
                }
                {
                    title && (
                        <h1
                            class="text-4xl md:text-5xl font-bold mb-4 font-heading dark:text-gray-200 intersect-once motion-safe:md:intersect:animate-fade motion-safe:md:opacity-0 intersect-quarter"
                            set:html={title}
                        />
                    )
                }
                <div class="max-w-3xl mx-auto lg:max-w-none">
                    {
                        subtitle && (
                            <p
                                class="text-xl text-muted mb-6 dark:text-slate-300 intersect-once motion-safe:md:intersect:animate-fade motion-safe:md:opacity-0 intersect-quarter"
                                set:html={subtitle}
                            />
                        )
                    }

                    {
                        actions && (
                            <div class="max-w-xs sm:max-w-md m-auto flex flex-nowrap flex-col sm:flex-row sm:justify-center gap-4 lg:justify-start lg:m-0 lg:max-w-7xl intersect-once motion-safe:md:intersect:animate-fade motion-safe:md:opacity-0 intersect-quarter">
                                {Array.isArray(actions) ? (
                                    actions.map((action) => (
                                        <div class="flex w-full sm:w-auto">
                                            <Button
                                                {...(action || {})}
                                                class="w-full sm:mb-0"
                                            />
                                        </div>
                                    ))
                                ) : (
                                    <Fragment set:html={actions} />
                                )}
                            </div>
                        )
                    }
                </div>
                {content && <Fragment set:html={content} />}
            </div>
            <div class="basis-1/2">
                {
                    image && (
                        <div class="relative m-auto max-w-5xl intersect-once intercept-no-queue motion-safe:md:intersect:animate-fade motion-safe:md:opacity-0 intersect-quarter">
                            {typeof image === "string" ? (
                                <Fragment set:html={image} />
                            ) : (
                                <Image
                                    class="mx-auto rounded-md w-full"
                                    widths={[400, 768, 1024, 2040]}
                                    sizes="(max-width: 767px) 400px, (max-width: 1023px) 768px, (max-width: 2039px) 1024px, 2040px"
                                    loading="eager"
                                    width={600}
                                    height={430}
                                    {...image}
                                />
                            )}
                        </div>
                    )
                }
            </div>
        </div>
    </div>
</section>

<style>
    .light-mode-only {
        position: absolute;
        display: block;
    }
    html.dark .light-mode-only {
        display: none;
    }
    .dark-mode-only {
        display: none;
    }
    html.dark .dark-mode-only {
        position: absolute;
        width: 96vw;
        height: 90vh;

        display: block;
    }

    .aura-container {
        position: absolute;
        width: 90vw;
        height: 90vh;
        opacity: 0.4;
    }

    .aura {
        position: absolute;
        top: 50%;
        left: 50%;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        transform: translate(-50%, -50%);
        z-index: -2;
        background-image: conic-gradient(
            from var(--au),
            #f3b599,
            #f8dca8,
            #f3b663,
            #e45c71,
            #f3b599
        );
        background-size: cover;
        background-position: center;
        animation: a 10s linear infinite;
        filter: brightness(100%) contrast(150%) saturate(100%) blur(40px)
            hue-rotate(0deg);
        opacity: 0.6;
    }

    @property --au {
        syntax: "<angle>";
        initial-value: 0deg;
        inherits: false;
    }

    @keyframes a {
        to {
            --au: 1turn;
        }
    }

    @keyframes continuous-rotate-scale {
        0% {
            transform: rotate(0deg) scale(0.8);
        }
        50% {
            transform: rotate(180deg) scale(1);
        }
        100% {
            transform: rotate(360deg) scale(0.8);
        }
    }

    #sky {
        width: 100%;
        height: 50vh;
        margin: 0 auto;
        background: transparent;
        background-size: cover;
        position: relative;
        overflow: hidden;
    }
    #sky .shooting {
        height: 200vh;
        width: 1px;
        position: absolute;
        top: -300px;
        left: 0%;
        transform: rotate(45deg);
    }
    #sky .shooting .star {
        width: 1px;
        height: 35px;
        background: linear-gradient(
            to bottom,
            rgba(255, 255, 255, 0) 0%,
            #fff 75%,
            #fff 100%
        );
        -webkit-animation: run 6s 0s infinite;
        animation: run 6s 0s infinite;
        -webkit-animation-fill-mode: forwards;
        animation-fill-mode: forwards;
        -webkit-animation-timing-function: cubic-bezier(0.4, 0.26, 0, 0.97);
        animation-timing-function: cubic-bezier(0.4, 0.26, 0, 0.97);
    }
    #sky .shooting:nth-of-type(2) {
        left: 30%;
    }
    #sky .shooting:nth-of-type(2) .star {
        -webkit-animation-delay: 1.5s;
        animation-delay: 1.5s;
    }
    #sky .shooting:nth-of-type(3) {
        left: 60%;
    }
    #sky .shooting:nth-of-type(3) .star {
        -webkit-animation-delay: 2.5s;
        animation-delay: 2.5s;
    }
    #sky .shooting:nth-of-type(4) {
        left: 90%;
    }
    #sky .shooting:nth-of-type(4) .star {
        -webkit-animation-delay: 1.8s;
        animation-delay: 1.8s;
    }
    #sky .shooting:nth-of-type(5) {
        left: 10%;
    }
    #sky .shooting:nth-of-type(5) .star {
        -webkit-animation-delay: 1.2s;
        animation-delay: 1.2s;
    }
    #sky .shooting:nth-of-type(6) {
        left: 30%;
    }
    #sky .shooting:nth-of-type(6) .star {
        -webkit-animation-delay: 2.2s;
        animation-delay: 2.2s;
    }
    @-webkit-keyframes run {
        0% {
            margin-top: 0;
            opacity: 0;
        }
        100% {
            margin-top: 900px;
        }
    }
    @keyframes run {
        0% {
            margin-top: 0;
            opacity: 0;
        }
        100% {
            margin-top: 900px;
        }
    }

    .stars {
        min-height: 100vh;
        background-image:
            radial-gradient(
                circle,
                rgba(255, 255, 255, 0.7) 20%,
                transparent 30%
            ),
            radial-gradient(
                circle,
                rgba(255, 255, 255, 0.7) 20%,
                transparent 30%
            ),
            radial-gradient(
                circle,
                rgba(255, 255, 255, 0.7) 20%,
                transparent 30%
            ),
            radial-gradient(
                circle,
                rgba(255, 255, 255, 0.7) 20%,
                transparent 30%
            ),
            radial-gradient(
                circle,
                rgba(255, 255, 255, 0.7) 20%,
                transparent 30%
            ),
            radial-gradient(
                circle,
                rgba(255, 255, 255, 0.7) 20%,
                transparent 30%
            ),
            radial-gradient(
                circle,
                rgba(255, 255, 255, 0.7) 20%,
                transparent 30%
            ),
            radial-gradient(
                circle,
                rgba(255, 255, 255, 0.7) 20%,
                transparent 30%
            ),
            radial-gradient(
                circle,
                rgba(255, 255, 255, 0.7) 20%,
                transparent 30%
            ),
            radial-gradient(
                circle,
                rgba(255, 255, 255, 0.7) 20%,
                transparent 30%
            ),
            radial-gradient(
                circle,
                rgba(255, 255, 255, 0.7) 20%,
                transparent 30%
            ),
            radial-gradient(
                circle,
                rgba(255, 255, 255, 0.7) 20%,
                transparent 30%
            ),
            radial-gradient(
                circle,
                rgba(255, 255, 255, 0.7) 20%,
                transparent 30%
            ),
            radial-gradient(
                circle,
                rgba(255, 255, 255, 0.7) 20%,
                transparent 30%
            ),
            radial-gradient(
                circle,
                rgba(255, 255, 255, 0.7) 20%,
                transparent 30%
            ),
            radial-gradient(
                circle,
                rgba(255, 255, 255, 0.7) 20%,
                transparent 30%
            ),
            radial-gradient(
                circle,
                rgba(255, 255, 255, 0.7) 20%,
                transparent 30%
            ),
            radial-gradient(
                circle,
                rgba(255, 255, 255, 0.7) 20%,
                transparent 30%
            );
        background-size:
            4px 4px,
            3px 3px,
            5px 5px,
            3px 3px,
            6px 6px,
            4px 4px,
            5px 5px,
            4px 4px,
            6px 6px,
            3px 3px,
            5px 5px,
            4px 4px,
            3px 3px,
            6px 6px,
            5px 5px,
            4px 4px,
            5px 5px,
            6px 6px;
        background-position:
            20% 10%,
            5% 20%,
            10% 75%,
            22% 35%,
            30% 30%,
            40% 50%,
            45% 20%,
            65% 20%,
            85% 30%,
            98% 90%,
            20% 80%,
            50% 80%,
            75% 80%,
            89% 75%,
            90% 95%,
            60% 70%,
            65% 40%,
            90% 20%;
        background-repeat: no-repeat;
        animation: twinkle ease-in 0.3s infinite;
    }

    @keyframes twinkle {
        to {
            background-image:
                radial-gradient(
                    circle,
                    rgba(255, 255, 255, 0.7) 20%,
                    transparent 30%
                ),
                radial-gradient(circle, #fff 20%, transparent 30%),
                radial-gradient(
                    circle,
                    rgba(255, 255, 255, 0.7) 20%,
                    transparent 30%
                ),
                radial-gradient(
                    circle,
                    rgba(255, 255, 255, 0.7) 20%,
                    transparent 30%
                ),
                radial-gradient(circle, #fff 20%, transparent 30%),
                radial-gradient(
                    circle,
                    rgba(255, 255, 255, 0.7) 20%,
                    transparent 30%
                ),
                radial-gradient(
                    circle,
                    rgba(255, 255, 255, 0.7) 20%,
                    transparent 30%
                ),
                radial-gradient(circle, #fff 20%, transparent 30%),
                radial-gradient(
                    circle,
                    rgba(255, 255, 255, 0.7) 20%,
                    transparent 30%
                ),
                radial-gradient(circle, #fff 20%, transparent 30%),
                radial-gradient(
                    circle,
                    rgba(255, 255, 255, 0.7) 20%,
                    transparent 30%
                ),
                radial-gradient(circle, #fff 20%, transparent 30%),
                radial-gradient(
                    circle,
                    rgba(255, 255, 255, 0.7) 20%,
                    transparent 30%
                ),
                radial-gradient(
                    circle,
                    rgba(255, 255, 255, 0.7) 20%,
                    transparent 30%
                ),
                radial-gradient(circle, #fff 20%, transparent 30%),
                radial-gradient(
                    circle,
                    rgba(255, 255, 255, 0.7) 20%,
                    transparent 30%
                ),
                radial-gradient(circle, #fff 20%, transparent 30%),
                radial-gradient(circle, #fff 20%, transparent 30%);
        }
    }
</style>
