---
import { Icon } from 'astro-icon/components';
import { SITE } from 'astrowind:config';
import { getHomePermalink } from '~/utils/permalinks';

interface Link {
  text?: string;
  href?: string;
  ariaLabel?: string;
  icon?: string;
}

interface Links {
  title?: string;
  links: Array<Link>;
}

export interface Props {
  links: Array<Links>;
  secondaryLinks: Array<Link>;
  socialLinks: Array<Link>;
  footNote?: string;
  theme?: string;
}

const { socialLinks = [], secondaryLinks = [], links = [], footNote = '', theme = 'light' } = Astro.props;
---

<footer class:list={[{ dark: theme === 'dark' }, 'relative border-t border-gray-200 dark:border-slate-800 not-prose']}>
  <div class="dark:bg-dark absolute inset-0 pointer-events-none" aria-hidden="true"></div>
  <div
    class="relative max-w-7xl mx-auto px-4 sm:px-6 dark:text-slate-300 intersect-once intersect-quarter intercept-no-queue motion-safe:md:opacity-0 motion-safe:md:intersect:animate-fade">
    <div class="grid grid-cols-12 gap-4 gap-y-8 sm:gap-8 py-8 md:py-12">
      <div class="col-span-12 lg:col-span-4">
        <div class="mb-2">
          <a class="inline-block font-bold text-xl" href={getHomePermalink()}>{SITE?.name}</a>
        </div>
        <div class="text-sm text-muted flex gap-1">
          {
            secondaryLinks.map(({ text, href }, index) => (
              <>
                {index !== 0 ? ' · ' : ''}
                <a
                  class="text-muted hover:text-gray-700 dark:text-gray-400 hover:underline transition duration-150 ease-in-out"
                  href={href}
                  set:html={text}
                />
              </>
            ))
          }
        </div>
      </div>
      {
        links.map(({ title, links }) => (
          <div class="col-span-6 md:col-span-3 lg:col-span-2">
            <div class="dark:text-gray-300 font-medium mb-2">{title}</div>
            {links && Array.isArray(links) && links.length > 0 && (
              <ul class="text-sm">
                {links.map(({ text, href, ariaLabel }) => (
                  <li class="mb-2">
                    <a
                      class="text-muted hover:text-gray-700 hover:underline dark:text-gray-400 transition duration-150 ease-in-out"
                      href={href}
                      aria-label={ariaLabel}>
                      <Fragment set:html={text} />
                    </a>
                  </li>
                ))}
              </ul>
            )}
          </div>
        ))
      }
    </div>
    <div class="md:flex md:items-center md:justify-between py-6 md:py-8">
      {
        socialLinks?.length ? (
          <ul class="flex mb-4 md:order-1 -ml-2 md:ml-4 md:mb-0 rtl:ml-0 rtl:-mr-2 rtl:md:ml-0 rtl:md:mr-4">
            {socialLinks.map(({ ariaLabel, href, text, icon }) => (
              <li>
                <a
                  class="text-muted dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 rounded-lg text-sm p-2.5 inline-flex items-center"
                  aria-label={ariaLabel}
                  href={href}>
                  {icon && <Icon name={icon} class="w-5 h-5" />}
                  <Fragment set:html={text} />
                </a>
              </li>
            ))}
          </ul>
        ) : (
          ''
        )
      }

      <div class="text-sm mr-4 dark:text-muted">
        <Fragment set:html={footNote} />
      </div>
    </div>
  </div>
</footer>
