---
import { APP_BLOG } from 'astrowind:config';
import type { Post } from '~/types';

import Image from '~/components/common/Image.astro';

import { findImage } from '~/utils/images';
import { getPermalink } from '~/utils/permalinks';

export interface Props {
  post: Post;
}

const { post } = Astro.props;
const image = await findImage(post.image);

const link = APP_BLOG?.post?.isEnabled ? getPermalink(post.permalink, 'post') : '';
---

<article
  class="mb-6 transition intersect-once intersect-quarter motion-safe:md:opacity-0 motion-safe:md:intersect:animate-fade"
>
  <div class="relative md:h-64 bg-gray-400 dark:bg-slate-700 rounded shadow-lg mb-6">
    {
      image &&
        (link ? (
          <a href={link}>
            <Image
              src={image}
              class="w-full md:h-full rounded shadow-lg bg-gray-400 dark:bg-slate-700"
              widths={[400, 900]}
              width={400}
              sizes="(max-width: 900px) 400px, 900px"
              alt={post.title}
              aspectRatio="16:9"
              layout="cover"
              loading="lazy"
              decoding="async"
            />
          </a>
        ) : (
          <Image
            src={image}
            class="w-full md:h-full rounded shadow-lg bg-gray-400 dark:bg-slate-700"
            widths={[400, 900]}
            width={400}
            sizes="(max-width: 900px) 400px, 900px"
            alt={post.title}
            aspectRatio="16:9"
            layout="cover"
            loading="lazy"
            decoding="async"
          />
        ))
    }
  </div>

  <h3 class="text-xl sm:text-2xl font-bold leading-tight mb-2 font-heading dark:text-slate-300">
    {
      link ? (
        <a class="inline-block hover:text-primary dark:hover:text-blue-700 transition ease-in duration-200" href={link}>
          {post.title}
        </a>
      ) : (
        post.title
      )
    }
  </h3>

  <p class="text-muted dark:text-slate-400 text-lg">{post.excerpt}</p>
</article>
