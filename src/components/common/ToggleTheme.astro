---
import { Icon } from 'astro-icon/components';

import { UI } from 'astrowind:config';

export interface Props {
  label?: string;
  class?: string;
  iconClass?: string;
}

const {
  label = 'Toggle between Dark and Light mode',
  class:
    className = 'text-muted dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 rounded-lg text-sm p-2.5 inline-flex items-center',
  iconClass = 'w-6 h-6',
} = Astro.props;
---

{
  !(UI.theme && UI.theme.endsWith(':only')) && (
    <button type="button" class={className} aria-label={label} data-aw-toggle-color-scheme>
      <!-- Sun icon - shown in dark mode (clicking switches to light) -->
      <Icon name="tabler:sun" class={`${iconClass} theme-toggle-sun`} />
      <!-- Moon icon - shown in light mode (clicking switches to dark) -->
      <Icon name="tabler:moon" class={`${iconClass} theme-toggle-moon`} />
    </button>
  )
}

<script>
  // Update theme toggle icons when theme changes
  function updateThemeToggleIcons() {
    const buttons = document.querySelectorAll('[data-aw-toggle-color-scheme]');
    buttons.forEach(button => {
      // Find icons using our custom classes
      const sunIcon = button.querySelector('.theme-toggle-sun') as HTMLElement;
      const moonIcon = button.querySelector('.theme-toggle-moon') as HTMLElement;

      const isDark = document.documentElement.classList.contains('dark');

      if (sunIcon && moonIcon) {
        if (isDark) {
          // Dark mode: show sun icon, hide moon icon
          sunIcon.style.display = 'block';
          moonIcon.style.display = 'none';
        } else {
          // Light mode: show moon icon, hide sun icon
          sunIcon.style.display = 'none';
          moonIcon.style.display = 'block';
        }
      }
    });
  }

  // Update icons after theme changes (for Astro page transitions)
  document.addEventListener('astro:after-swap', updateThemeToggleIcons);

  // Update icons when theme toggle is clicked
  document.addEventListener('click', function(e) {
    const target = e.target as Element;
    if (target && target.closest('[data-aw-toggle-color-scheme]')) {
      // Small delay to allow the theme change to complete
      setTimeout(updateThemeToggleIcons, 10);
    }
  });

  // Run immediately if DOM is already loaded, otherwise wait for DOMContentLoaded
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', updateThemeToggleIcons);
  } else {
    updateThemeToggleIcons();
  }
</script>
