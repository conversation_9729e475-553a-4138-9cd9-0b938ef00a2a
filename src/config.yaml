site:
    name: Nimarmi
    site: "https://nimarmi.com"
    base: "/"
    trailingSlash: false

    googleSiteVerificationId: orcPxI47GSa-cRvY11tUe6iGg2IO_RPvnA1q95iEM3M

# Default SEO metadata
metadata:
    title:
        default: Nimarmi
        template: "%s — Nimarmi"
    description: "Professional web design and branding for small businesses. Beautiful websites, modern logos, and complete brand packages that help your business stand out."
    robots:
        index: true
        follow: true
    openGraph:
        site_name: Nimarmi
        images:
            - url: "~/assets/images/default.png"
              width: 1200
              height: 628
        type: website
    twitter:
        handle: "@nimarmi"
        site: "@nimarmi"
        cardType: summary_large_image

i18n:
    language: en
    textDirection: ltr

apps:
    blog:
        isEnabled: true
        postsPerPage: 6

        post:
            isEnabled: true
            permalink: "/%slug%" # Variables: %slug%, %year%, %month%, %day%, %hour%, %minute%, %second%, %category%
            robots:
                index: true

        list:
            isEnabled: true
            pathname: "blog" # Blog main path, you can change this to "articles" (/articles)
            robots:
                index: true

        category:
            isEnabled: true
            pathname: "category" # Category main path /category/some-category, you can change this to "group" (/group/some-category)
            robots:
                index: true

        tag:
            isEnabled: true
            pathname: "tag" # Tag main path /tag/some-tag, you can change this to "topics" (/topics/some-category)
            robots:
                index: false

        isRelatedPostsEnabled: true
        relatedPostsCount: 4

analytics:
    vendors:
        googleAnalytics:
            id: null # or "G-XXXXXXXXXX"

ui:
    theme: "system" # Values: "system" | "light" | "dark" | "light:only" | "dark:only"
