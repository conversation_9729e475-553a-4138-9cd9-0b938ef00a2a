---
import Layout from '~/layouts/PageLayout.astro';
import HeroText from '~/components/widgets/HeroText.astro';
import ContactUs from '~/components/widgets/Contact.astro';
import Features2 from '~/components/widgets/Features2.astro';

const metadata = {
  title: 'Contact',
};
---

<Layout metadata={metadata}>
  <!-- HeroText Widget ******************* -->

  <HeroText tagline="Contact" title="Let's Connect!" />

  <ContactUs
    id="form"
    title="Drop us a message today!"
    subtitle="For quicker answers, explore our FAQs section. You may find the solution you're looking  for right there! If not, our support team is delighted to help you."
    inputs={[
      {
        type: 'text',
        name: 'name',
        label: 'Name',
      },
      {
        type: 'email',
        name: 'email',
        label: 'Email',
      },
    ]}
    textarea={{
      label: 'Message',
    }}
    disclaimer={{
      label:
        'By submitting this contact form, you acknowledge and agree to the collection of your personal information.',
    }}
    description="Our support team typically responds within 24 business hours."
  />

  <!-- Features2 Widget ************** -->

  <Features2
    title="We are here to help!"
    items={[
      {
        title: 'General support',
        description: `Chat with us for inquiries related to account management, website navigation, payment issues, accessing purchased templates or general questions about the website's functionality.`,
      },
      {
        title: 'Contact sales',
        description:
          'Chat with us for questions about purchases, customization options, licensing for commercial use, inquiries about specific template, etc.',
      },
      {
        title: 'Technical support',
        description:
          'Chat with us when facing issues like template installation, problems editing difficulties, compatibility issues with software or download errors, or other technical challenges related to using the templates.',
      },
      {
        title: 'Phone',
        description: '+1 (234) 567-890',
        icon: 'tabler:headset',
      },
      {
        title: 'Email',
        description: '<EMAIL>',
        icon: 'tabler:mail',
      },
      {
        title: 'Location',
        description: '1234 Lorem Ipsum St, 12345, Miami, EEUU',
        icon: 'tabler:map-pin',
      },
    ]}
  />
</Layout>
