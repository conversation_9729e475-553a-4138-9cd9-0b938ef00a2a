import { getPermalink, getBlogPermalink, getAsset } from "./utils/permalinks";
const currentYear = new Date().getFullYear();
export const headerData = {
    links: [
        {
            text: "Home",
            href: "/",
        },
        {
            text: "About us",
            href: "about",
        },

        {
            text: "Services",
            href: "services",
            links: [
                {
                    text: "Service",
                    href: getPermalink("/landing/lead-generation"),
                },
                {
                    text: "Long-form Sales",
                    href: getPermalink("/landing/sales"),
                },
                {
                    text: "Click-Through",
                    href: getPermalink("/landing/click-through"),
                },
                {
                    text: "Product Details (or Services)",
                    href: getPermalink("/landing/product"),
                },
                {
                    text: "Coming Soon or Pre-Launch",
                    href: getPermalink("/landing/pre-launch"),
                },
                {
                    text: "Subscription",
                    href: getPermalink("/landing/subscription"),
                },
            ],
        },
        {
            text: "Locations",
            href: "locations",
            links: [
                {
                    text: "Illinois",
                    href: getPermalink("/landing/illinois"),
                },
                {
                    text: "Indiana",
                    href: getPermalink("/landing/indiana"),
                },
                {
                    text: "Wisconsin",
                    href: getPermalink("/landing/wisconsin"),
                },
            ],
        },
        {
            text: "Blog",
            href: getPermalink("/blog"),
        },
        {
            text: "Contact",
            href: getPermalink("/contact"),
        },
    ],
    actions: [
        {
            text: "Get Started",
            href: getPermalink("/contact"),
            target: "_self",
        },
    ],
};

export const footerData = {
    links: [
        {
            title: "Product",
            links: [
                { text: "Features", href: "#" },
                { text: "Security", href: "#" },
                { text: "Team", href: "#" },
                { text: "Enterprise", href: "#" },
                { text: "Customer stories", href: "#" },
                { text: "Pricing", href: "#" },
                { text: "Resources", href: "#" },
            ],
        },
        {
            title: "Platform",
            links: [
                { text: "Developer API", href: "#" },
                { text: "Partners", href: "#" },
                { text: "Atom", href: "#" },
                { text: "Electron", href: "#" },
                { text: "AstroWind Desktop", href: "#" },
            ],
        },
        {
            title: "Support",
            links: [
                { text: "Docs", href: "#" },
                { text: "Community Forum", href: "#" },
                { text: "Professional Services", href: "#" },
                { text: "Skills", href: "#" },
                { text: "Status", href: "#" },
            ],
        },
        {
            title: "Company",
            links: [
                { text: "About", href: "#" },
                { text: "Blog", href: "#" },
                { text: "Careers", href: "#" },
                { text: "Press", href: "#" },
                { text: "Inclusion", href: "#" },
                { text: "Social Impact", href: "#" },
                { text: "Shop", href: "#" },
            ],
        },
    ],
    secondaryLinks: [
        { text: "Terms", href: getPermalink("/terms") },
        { text: "Privacy Policy", href: getPermalink("/privacy") },
    ],
    socialLinks: [
        {
            ariaLabel: "X",
            icon: "tabler:brand-x",
            href: "https://x.com/itsnimarmi",
        },
        {
            ariaLabel: "Instagram",
            icon: "tabler:brand-instagram",
            href: "https://www.instagram.com/itsnimarmi/",
        },
        {
            ariaLabel: "Facebook",
            icon: "tabler:brand-facebook",
            href: "https://www.facebook.com/profile.php?id=61576677014500",
        },
        {
            ariaLabel: "BlueSky",
            icon: "tabler:brand-bluesky",
            href: "https://bsky.app/profile/nimarmi.com",
        },
        { ariaLabel: "RSS", icon: "tabler:rss", href: getAsset("/rss.xml") },
    ],
    footNote: `
        <p class="mb-2">
            &copy; ${currentYear}
            <span>nimarmi.com</span>. All rights reserved.
        </p>
  `,
};
