{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"site": {"type": "object", "properties": {"name": {"type": "string"}, "site": {"type": "string"}, "base": {"type": "string"}, "trailingSlash": {"type": "boolean"}, "googleSiteVerificationId": {"type": "string"}}, "required": ["name", "site", "base", "trailingSlash"], "additionalProperties": true}, "metadata": {"type": "object", "properties": {"title": {"type": "object", "properties": {"default": {"type": "string"}, "template": {"type": "string"}}, "required": ["default", "template"]}, "description": {"type": "string"}, "robots": {"type": "object", "properties": {"index": {"type": "boolean"}, "follow": {"type": "boolean"}}, "required": ["index", "follow"]}, "openGraph": {"type": "object", "properties": {"site_name": {"type": "string"}, "images": {"type": "array", "items": [{"type": "object", "properties": {"url": {"type": "string"}, "width": {"type": "integer"}, "height": {"type": "integer"}}, "required": ["url", "width", "height"]}]}, "type": {"type": "string"}}, "required": ["site_name", "images", "type"]}, "twitter": {"type": "object", "properties": {"handle": {"type": "string"}, "site": {"type": "string"}, "cardType": {"type": "string"}}, "required": ["handle", "site", "cardType"]}}, "required": ["title", "description", "robots", "openGraph", "twitter"]}, "i18n": {"type": "object", "properties": {"language": {"type": "string"}, "textDirection": {"type": "string"}}, "required": ["language", "textDirection"]}, "apps": {"type": "object", "properties": {"blog": {"type": "object", "properties": {"isEnabled": {"type": "boolean"}, "postsPerPage": {"type": "integer"}, "isRelatedPostsEnabled": {"type": "boolean"}, "relatedPostsCount": {"type": "integer"}, "post": {"type": "object", "properties": {"isEnabled": {"type": "boolean"}, "permalink": {"type": "string"}, "robots": {"type": "object", "properties": {"index": {"type": "boolean"}, "follow": {"type": "boolean"}}, "required": ["index"]}}, "required": ["isEnabled", "permalink", "robots"]}, "list": {"type": "object", "properties": {"isEnabled": {"type": "boolean"}, "pathname": {"type": "string"}, "robots": {"type": "object", "properties": {"index": {"type": "boolean"}, "follow": {"type": "boolean"}}, "required": ["index"]}}, "required": ["isEnabled", "pathname", "robots"]}, "category": {"type": "object", "properties": {"isEnabled": {"type": "boolean"}, "pathname": {"type": "string"}, "robots": {"type": "object", "properties": {"index": {"type": "boolean"}, "follow": {"type": "boolean"}}, "required": ["index"]}}, "required": ["isEnabled", "pathname", "robots"]}, "tag": {"type": "object", "properties": {"isEnabled": {"type": "boolean"}, "pathname": {"type": "string"}, "robots": {"type": "object", "properties": {"index": {"type": "boolean"}, "follow": {"type": "boolean"}}, "required": ["index"]}}, "required": ["isEnabled", "pathname", "robots"]}}, "required": ["isEnabled", "postsPerPage", "post", "list", "category", "tag"]}}, "required": ["blog"]}, "analytics": {"type": "object", "properties": {"vendors": {"type": "object", "properties": {"googleAnalytics": {"type": "object", "properties": {"id": {"type": ["string", "null"]}, "partytown": {"type": "boolean", "default": true}}, "required": ["id"]}}, "required": ["googleAnalytics"]}}, "required": ["vendors"]}, "ui": {"type": "object", "properties": {"theme": {"type": "string"}}, "required": ["theme"]}}, "required": ["site", "metadata", "i18n", "apps", "analytics", "ui"]}